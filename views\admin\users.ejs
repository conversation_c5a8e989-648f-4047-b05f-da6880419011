<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %> - Telegram卡密销售系统</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            border-radius: 8px;
            margin: 2px 0;
            transition: all 0.3s;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }
        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s;
        }
        .card:hover {
            transform: translateY(-2px);
        }
        .table th {
            border-top: none;
            background-color: #f8f9fa;
            font-weight: 600;
        }
        .badge-status {
            font-size: 0.75rem;
            padding: 0.375rem 0.75rem;
        }
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        .search-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .btn-outline-light:hover {
            color: #667eea;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h5 class="text-white">
                            <i class="fas fa-store me-2"></i>
                            卡密销售系统
                        </h5>
                        <small class="text-white-50">欢迎，<%= admin.username %></small>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link <%= currentPage === 'dashboard' ? 'active' : '' %>" href="/admin/dashboard">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                仪表板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <%= currentPage === 'products' ? 'active' : '' %>" href="/admin/products">
                                <i class="fas fa-box me-2"></i>
                                商品管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <%= currentPage === 'orders' ? 'active' : '' %>" href="/admin/orders">
                                <i class="fas fa-shopping-cart me-2"></i>
                                订单管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <%= currentPage === 'cards' ? 'active' : '' %>" href="/admin/cards">
                                <i class="fas fa-credit-card me-2"></i>
                                卡密管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <%= currentPage === 'users' ? 'active' : '' %>" href="/admin/users">
                                <i class="fas fa-users me-2"></i>
                                用户管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <%= currentPage === 'reports' ? 'active' : '' %>" href="/admin/reports">
                                <i class="fas fa-chart-bar me-2"></i>
                                统计报表
                            </a>
                        </li>
                        <li class="nav-item mt-3">
                            <a class="nav-link" href="/admin/logout">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                退出登录
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-users me-2"></i>
                        用户管理
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-download me-1"></i>
                                导出用户
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-envelope me-1"></i>
                                群发消息
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 搜索栏 -->
                <div class="card search-card mb-4">
                    <div class="card-body">
                        <form method="GET" action="/admin/users" class="row g-3">
                            <div class="col-md-6">
                                <label for="search" class="form-label">搜索用户</label>
                                <input type="text" class="form-control" id="search" name="search" 
                                       placeholder="输入用户名或Telegram ID" value="<%= search || '' %>">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-outline-light">
                                        <i class="fas fa-search me-1"></i>
                                        搜索
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <a href="/admin/users" class="btn btn-outline-light">
                                        <i class="fas fa-refresh me-1"></i>
                                        重置
                                    </a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 用户列表 -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold">
                            <i class="fas fa-list me-2"></i>
                            用户列表
                            <span class="badge bg-primary ms-2">
                                共 <%= pagination.total || 0 %> 个用户
                            </span>
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>头像</th>
                                        <th>用户信息</th>
                                        <th>Telegram ID</th>
                                        <th>订单统计</th>
                                        <th>消费金额</th>
                                        <th>注册时间</th>
                                        <th>最后活跃</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <% if (users && users.length > 0) { %>
                                        <% users.forEach(user => { %>
                                            <tr>
                                                <td>
                                                    <div class="user-avatar">
                                                        <%= (user.username || user.telegram_id.toString()).charAt(0).toUpperCase() %>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div>
                                                        <strong>
                                                            <% if (user.username) { %>
                                                                @<%= user.username %>
                                                            <% } else { %>
                                                                用户<%= user.telegram_id %>
                                                            <% } %>
                                                        </strong>
                                                        <% if (user.first_name || user.last_name) { %>
                                                            <br>
                                                            <small class="text-muted">
                                                                <%= (user.first_name || '') + ' ' + (user.last_name || '') %>
                                                            </small>
                                                        <% } %>
                                                    </div>
                                                </td>
                                                <td><code><%= user.telegram_id %></code></td>
                                                <td>
                                                    <span class="badge bg-info badge-status">
                                                        <%= user.order_count || 0 %> 订单
                                                    </span>
                                                </td>
                                                <td>
                                                    <strong>¥<%= parseFloat(user.total_spent || 0).toFixed(2) %></strong>
                                                </td>
                                                <td><%= new Date(user.created_at).toLocaleString('zh-CN') %></td>
                                                <td>
                                                    <% if (user.last_active_at) { %>
                                                        <%= new Date(user.last_active_at).toLocaleString('zh-CN') %>
                                                    <% } else { %>
                                                        <span class="text-muted">从未活跃</span>
                                                    <% } %>
                                                </td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <button type="button" class="btn btn-outline-primary btn-sm" 
                                                                title="查看详情" onclick="viewUser(<%= user.id %>)">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-outline-success btn-sm" 
                                                                title="发送消息" onclick="sendMessage(<%= user.telegram_id %>)">
                                                            <i class="fas fa-envelope"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-outline-warning btn-sm" 
                                                                title="禁用用户" onclick="toggleUser(<%= user.id %>)">
                                                            <i class="fas fa-ban"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        <% }); %>
                                    <% } else { %>
                                        <tr>
                                            <td colspan="8" class="text-center text-muted py-4">
                                                <i class="fas fa-users fa-3x mb-3 opacity-25"></i>
                                                <p>暂无用户数据</p>
                                            </td>
                                        </tr>
                                    <% } %>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 分页 -->
                <% if (pagination && pagination.totalPages > 1) { %>
                    <nav aria-label="用户列表分页" class="mt-4">
                        <ul class="pagination justify-content-center">
                            <% if (pagination.hasPrev) { %>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<%= pagination.page - 1 %>&search=<%= search || '' %>">
                                        <i class="fas fa-chevron-left"></i>
                                    </a>
                                </li>
                            <% } else { %>
                                <li class="page-item disabled">
                                    <span class="page-link">
                                        <i class="fas fa-chevron-left"></i>
                                    </span>
                                </li>
                            <% } %>

                            <% for (let i = Math.max(1, pagination.page - 2); i <= Math.min(pagination.totalPages, pagination.page + 2); i++) { %>
                                <li class="page-item <%= i === pagination.page ? 'active' : '' %>">
                                    <a class="page-link" href="?page=<%= i %>&search=<%= search || '' %>">
                                        <%= i %>
                                    </a>
                                </li>
                            <% } %>

                            <% if (pagination.hasNext) { %>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<%= pagination.page + 1 %>&search=<%= search || '' %>">
                                        <i class="fas fa-chevron-right"></i>
                                    </a>
                                </li>
                            <% } else { %>
                                <li class="page-item disabled">
                                    <span class="page-link">
                                        <i class="fas fa-chevron-right"></i>
                                    </span>
                                </li>
                            <% } %>
                        </ul>
                    </nav>
                <% } %>
            </main>
        </div>
    </div>

    <!-- 用户详情模态框 -->
    <div class="modal fade" id="userDetailModal" tabindex="-1" aria-labelledby="userDetailModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="userDetailModalLabel">
                        <i class="fas fa-user me-2"></i>
                        用户详情
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="userDetailContent">
                    <!-- 用户详情内容将通过JavaScript加载 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // 查看用户详情
        function viewUser(userId) {
            fetch(`/api/users/${userId}`)
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        const user = result.data;
                        const content = `
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>基本信息</h6>
                                    <table class="table table-sm">
                                        <tr><td>用户名:</td><td>${user.username || '未设置'}</td></tr>
                                        <tr><td>姓名:</td><td>${(user.first_name || '') + ' ' + (user.last_name || '')}</td></tr>
                                        <tr><td>Telegram ID:</td><td>${user.telegram_id}</td></tr>
                                        <tr><td>注册时间:</td><td>${new Date(user.created_at).toLocaleString('zh-CN')}</td></tr>
                                        <tr><td>最后活跃:</td><td>${user.last_active_at ? new Date(user.last_active_at).toLocaleString('zh-CN') : '从未活跃'}</td></tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <h6>统计信息</h6>
                                    <table class="table table-sm">
                                        <tr><td>订单总数:</td><td>${user.order_count || 0}</td></tr>
                                        <tr><td>消费总额:</td><td>¥${parseFloat(user.total_spent || 0).toFixed(2)}</td></tr>
                                        <tr><td>状态:</td><td><span class="badge bg-success">正常</span></td></tr>
                                    </table>
                                </div>
                            </div>
                        `;
                        document.getElementById('userDetailContent').innerHTML = content;
                        new bootstrap.Modal(document.getElementById('userDetailModal')).show();
                    } else {
                        alert('获取用户详情失败：' + result.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('获取用户详情失败，请重试');
                });
        }

        // 发送消息
        function sendMessage(telegramId) {
            const message = prompt('请输入要发送的消息:');
            if (message) {
                fetch('/api/bot/send-message', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        telegram_id: telegramId,
                        message: message
                    })
                })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        alert('消息发送成功！');
                    } else {
                        alert('发送失败：' + result.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('发送失败，请重试');
                });
            }
        }

        // 切换用户状态
        function toggleUser(userId) {
            if (confirm('确定要切换用户状态吗？')) {
                fetch(`/api/users/${userId}/toggle`, {
                    method: 'PUT'
                })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        alert('用户状态已更新！');
                        location.reload();
                    } else {
                        alert('操作失败：' + result.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('操作失败，请重试');
                });
            }
        }
    </script>
</body>
</html>
